CONFIG_PATH=/etc/core/app.conf
UAA_CA_ROOT=/etc/core/certificates/uaa_ca.pem
_REDIS_URL_CORE=redis://redis:6379?idle_timeout_seconds=30
SYNC_QUOTA=true
_REDIS_URL_REG=redis://redis:6379/1?idle_timeout_seconds=30

LOG_LEVEL=info
EXT_ENDPOINT=http://*************
DATABASE_TYPE=postgresql
POSTGRESQL_HOST=postgresql
POSTGRESQL_PORT=5432
POSTGRESQL_USERNAME=postgres
POSTGRESQL_PASSWORD=root123
POSTGRESQL_DATABASE=registry
POSTGRESQL_SSLMODE=disable
POSTGRESQL_MAX_IDLE_CONNS=100
POSTGRESQL_MAX_OPEN_CONNS=900
POSTGRESQL_CONN_MAX_LIFETIME=5m
POSTGRESQL_CONN_MAX_IDLE_TIME=0
REGISTRY_URL=http://registry:5000
PORTAL_URL=http://portal:8080
TOKEN_SERVICE_URL=http://core:8080/service/token
HARBOR_ADMIN_PASSWORD=Harbor12345
MAX_JOB_WORKERS=10
CORE_SECRET=U2gGhXD69OzdgA4A
JOBSERVICE_SECRET=TCk3l0lhzXqfWURn
WITH_TRIVY=False
CORE_URL=http://core:8080
CORE_LOCAL_URL=http://127.0.0.1:8080
JOBSERVICE_URL=http://jobservice:8080
TRIVY_ADAPTER_URL=http://trivy-adapter:8080
REGISTRY_STORAGE_PROVIDER_NAME=filesystem
READ_ONLY=false
RELOAD_KEY=
REGISTRY_CONTROLLER_URL=http://registryctl:8080
REGISTRY_CREDENTIAL_USERNAME=harbor_registry_user
REGISTRY_CREDENTIAL_PASSWORD=GYmdT0vuCFkup78GLGZN0nP4YiyIYz3K
CSRF_KEY=xsva8iXRHqCU3W5VSMYLF54P1yNLyvV9
ROBOT_SCANNER_NAME_PREFIX=ygBLe4iL
PERMITTED_REGISTRY_TYPES_FOR_PROXY_CACHE=docker-hub,harbor,azure-acr,ali-acr,aws-ecr,google-gcr,quay,docker-registry,github-ghcr,jfrog-artifactory

HTTP_PROXY=
HTTPS_PROXY=
NO_PROXY=localhost,nginx,exporter,redis,registryctl,db,postgresql,127.0.0.1,.local,log,portal,core,registry,jobservice,.internal,trivy-adapter

PORT=8080




QUOTA_UPDATE_PROVIDER=db
